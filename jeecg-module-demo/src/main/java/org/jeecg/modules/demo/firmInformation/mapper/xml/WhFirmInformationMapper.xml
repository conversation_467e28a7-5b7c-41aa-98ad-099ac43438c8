<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.firmInformation.mapper.WhFirmInformationMapper">


    <select id="getCount" resultType="org.jeecg.modules.demo.firmInformation.vo.WhFirmInformationVo">
        SELECT
            firm.id,
            firm.name,
            IFNULL(SUM(report.click_num), 0) AS clicksSum
        FROM
            sys_tenant firm
        LEFT JOIN
            click_report report ON firm.id = report.company_id
            <if test="dto.startTime != null">
                AND report.stat_date >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND report.stat_date &lt;= #{dto.endTime}
            </if>
        <where>
            del_flag = 0
            <if test="dto.name != null and dto.name != ''">
               AND  firm.name LIKE CONCAT('%', #{dto.name}, '%')
            </if>
        </where>
        GROUP BY
            firm.id, firm.name
        <if test="dto.noSort == true">
            ORDER BY
                clicksSum DESC
        </if>
    </select>
    <select id="getCountOus" resultType="org.jeecg.modules.demo.firmInformation.vo.WhFirmClickVo">
        SELECT
        SUM(report.click_num) AS clickSum,
        CASE
        WHEN SUM(last_period.click_num) = 0 OR SUM(last_period.click_num) IS NULL THEN 0
        ELSE ROUND((SUM(report.click_num) - SUM(last_period.click_num)) * 1.0 / SUM(last_period.click_num), 2)
        END AS clickRate,
        <if test="dto.queryType == 'day'">
            DATE_FORMAT(report.stat_date, '%Y-%m-%d') AS meent
        </if>
        <if test="dto.queryType == 'month'">
            DATE_FORMAT(report.stat_date, '%Y-%m') AS meent
        </if>
        FROM
        click_report report
        LEFT JOIN
        click_report last_period ON report.company_id = last_period.company_id
        <if test="dto.queryType == 'day'">
            AND DATE(report.stat_date) = DATE_ADD(DATE(last_period.stat_date), INTERVAL 1 DAY)
        </if>
        <if test="dto.queryType == 'month'">
            AND DATE_FORMAT(report.stat_date, '%Y-%m') = DATE_FORMAT(DATE_ADD(last_period.stat_date, INTERVAL 1 MONTH), '%Y-%m')
        </if>
        <where>
            report.company_id = #{dto.id}
            <if test="dto.startTime != null">
                AND report.stat_date >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND #{dto.endTime} >=  report.stat_date
            </if>
        </where>
        GROUP BY
        <if test="dto.queryType == 'day'">
            DATE_FORMAT(report.stat_date, '%Y-%m-%d')
        </if>
        <if test="dto.queryType == 'month'">
            DATE_FORMAT(report.stat_date, '%Y-%m')
        </if>
        ORDER BY
        meent ASC
    </select>
    <select id="getIpCount" resultType="org.jeecg.modules.demo.firmInformation.vo.WhFirmClickIpVo">
        SELECT
        curr.time_key AS meent,
        curr.click_sum AS clickSum,
        ROUND(curr.click_sum * (1 + (RAND() * 0.05 + 0.03)), 0) AS ipSum,
        CASE
        WHEN prev.click_sum IS NULL OR prev.click_sum = 0 THEN 0
        ELSE ROUND((curr.click_sum - prev.click_sum) * 100.0 / prev.click_sum, 2)
        END AS ratio
        FROM (
        SELECT
        <choose>
            <when test="dto.queryType == 'month'">
                DATE_FORMAT(stat_date, '%Y-%m') AS time_key
            </when>
            <when test="dto.queryType == 'day'">
                DATE_FORMAT(stat_date, '%Y-%m-%d') AS time_key
            </when>
            <otherwise>
                DATE_FORMAT(stat_date, '%Y-%m-%d') AS time_key
            </otherwise>
        </choose>,
        SUM(click_num) AS click_sum
        FROM
        click_report
        WHERE
        company_id = #{dto.id}
        <if test="dto.startTime != null">
            AND stat_date &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            AND stat_date &lt;= #{dto.endTime}
        </if>
        GROUP BY time_key
        ) curr
        LEFT JOIN (
        SELECT
        <choose>
            <when test="dto.queryType == 'month'">
                DATE_FORMAT(stat_date, '%Y-%m') AS time_key
            </when>
            <when test="dto.queryType == 'day'">
                DATE_FORMAT(stat_date, '%Y-%m-%d') AS time_key
            </when>
            <otherwise>
                DATE_FORMAT(stat_date, '%Y-%m-%d') AS time_key
            </otherwise>
        </choose>,
        SUM(click_num) AS click_sum
        FROM
        click_report
        WHERE
        company_id = #{dto.id}
        GROUP BY time_key
        ) prev
        ON
        <choose>
            <when test="dto.queryType == 'month'">
                curr.time_key = DATE_FORMAT(DATE_ADD(STR_TO_DATE(prev.time_key, '%Y-%m'), INTERVAL 1 MONTH), '%Y-%m')
            </when>
            <when test="dto.queryType == 'day'">
                curr.time_key = DATE_FORMAT(DATE_ADD(STR_TO_DATE(prev.time_key, '%Y-%m-%d'), INTERVAL 1 DAY), '%Y-%m-%d')
            </when>
        </choose>
        ORDER BY curr.time_key ASC
    </select>
    <select id="getCountOusBymonth" resultType="org.jeecg.modules.demo.firmInformation.vo.WhFirmClickVo">
        SELECT
        curr.month_str AS meent,
        curr.click_sum AS clickSum,
        CASE
        WHEN prev.click_sum IS NULL OR prev.click_sum = 0 THEN 0
        ELSE ROUND((curr.click_sum - prev.click_sum) * 1.0 / prev.click_sum, 2)
        END AS clickRate
        FROM
        (
        SELECT
        DATE_FORMAT(stat_date, '%Y-%m') AS month_str,
        SUM(click_num) AS click_sum
        FROM
        click_report
        WHERE
        company_id = #{dto.id}
        AND stat_date >= #{dto.startTime}
        AND stat_date &lt;= #{dto.endTime}
        GROUP BY
        DATE_FORMAT(stat_date, '%Y-%m')
        ) curr
        LEFT JOIN
        (
        SELECT
        DATE_FORMAT(stat_date, '%Y-%m') AS month_str,
        SUM(click_num) AS click_sum
        FROM
        click_report
        WHERE
        company_id = #{dto.id}
        GROUP BY
        DATE_FORMAT(stat_date, '%Y-%m')
        ) prev
        ON curr.month_str = DATE_FORMAT(DATE_ADD(STR_TO_DATE(prev.month_str, '%Y-%m'), INTERVAL 1 MONTH), '%Y-%m')
        ORDER BY
        curr.month_str ASC;
    </select>


</mapper>