package org.jeecg.modules.demo.produce.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wh_backgroun_config对象", description="后台配置")
public class WhBackgrounConfigVo implements Serializable {

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**总点击数*/
    @Excel(name = "总点击数", width = 15)
    @ApiModelProperty(value = "总点击数")
    private Integer clickSum;

    @ApiModelProperty(value = "累计点击数")
    private Long clickTotal;
    /**合作公司总数*/
    @Excel(name = "合作公司总数", width = 15)
    @ApiModelProperty(value = "合作公司总数")
    private String companySum;
    /**转换率*/
    @Excel(name = "转换率", width = 15)
    @ApiModelProperty(value = "柱状集合")
    private List<Integer> integerList;
    /**百分比*/
    @Excel(name = "百分比", width = 15)
    @ApiModelProperty(value = "百分比")
    private String percent;
    /**编码*/
    @Excel(name = "编码", width = 15)
    @ApiModelProperty(value = "编码")
    private String code;
    /**名称*/
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;
}
