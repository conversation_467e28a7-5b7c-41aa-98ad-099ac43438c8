package org.jeecg.modules.wechat.controller;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.firmInformation.entity.WhCarousel;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.jeecg.modules.wechat.config.PdChatConfig;
import org.jeecg.modules.wechat.config.ExcelExportConfig;
import org.jeecg.modules.wechat.entity.PdChat;
import org.jeecg.modules.wechat.service.IPdChatService;
import org.jeecg.modules.corp.util.ExcelUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.wechat.vo.chat.AiMessageVo;
import org.jeecg.modules.wechat.vo.chat.ChatListVO;
import org.jeecg.modules.wechat.vo.chat.ChatRecodeVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 聊天记录
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Api(tags="聊天记录")
@RestController
@RequestMapping("/wechat/pdChat")
@Slf4j
public class PdChatController extends JeecgController<PdChat, IPdChatService> {
	@Autowired
	private IPdChatService pdChatService;
	 @Autowired
	 private ISysTenantService sysTenantService;
	 @Autowired
	 private IPdGuestUsersService pdGuestUsersService;
	 @Autowired
	 private ExcelExportConfig excelExportConfig;


	 @ApiOperation(value = "聊天历史记录-列表")
	 @GetMapping(value = "/getCozeMessage")
	 public Result<String> getAiMessage(@RequestParam String content,@RequestParam String botId)  {
		 String message = pdChatService.getAiMessage(content,botId);
		 return Result.OK(message);
	 }

	 @ApiOperation(value = "聊天配置-新增")
	 @PostMapping(value = "/addChatConfig")
	 public Result<?> addChatConfig(@RequestBody PdChatConfig entity)  {
		 pdChatService.addChatConfig(entity);
		 return Result.OK();
	 }

	 @ApiOperation(value = "获取配置-根据类型")
	 @GetMapping(value = "/get/setting")
	 public Result<?> getSetting(@RequestParam String type)  {
		 PdChatConfig chatConfig = pdChatService.getSetting(type);
		 return Result.OK(chatConfig);
	 }


	@ApiOperation(value="聊天记录-分页列表查询", notes="聊天记录-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<PdChat>> queryPageList(
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="dateRange",required = false) String dateRange,
								   @RequestParam(name="tenantId",required = false) String tenantId,
								   HttpServletRequest req) {
		QueryWrapper<PdChat> queryWrapper = new QueryWrapper<>();
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 检查用户是否有权限查看所有租户数据
		boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

		// 如果用户没有权限查看所有租户数据，则按照原来的逻辑限制租户ID
		if (!hasAllDataPermission) {
			queryWrapper.in("c.tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		}
		// 如果用户有权限查看所有租户数据，则不添加租户ID过滤条件，但如果传入了特定的租户ID，则只查询该租户的数据

		if (StringUtils.isNotBlank(tenantId)) {
			queryWrapper.eq("c.tenant_id", tenantId);
		}
		if (StringUtils.isNotBlank(dateRange)) {
			String[] dates = dateRange.split(",");
			if (dates.length == 2) {
				String startDateStr = dates[0].trim();
				String endDateStr = dates[1].trim();

				String startDateTime = startDateStr + " 00:00:00";
				String endDateTime = endDateStr + " 23:59:59";

				queryWrapper.ge("c.send_time", startDateTime); // 起始时间
				queryWrapper.le("c.send_time", endDateTime);   // 结束时间
			}
		}
		Page<PdChat> page = new Page<PdChat>(pageNo, pageSize);
		// 使用XML联查方式获取游客名称
		IPage<PdChat> pageList = pdChatService.pageList(page, queryWrapper);
		return Result.OK(pageList);
	}

	 @ApiOperation(value="聊天记录-分页列表查询", notes="聊天记录-分页列表查询")
	 @GetMapping(value = "/fetchRecord/{userId}")
	 public Result<List<ChatRecodeVo>> fetchRecord(@PathVariable String userId) {
		 List<ChatRecodeVo> s = pdChatService.fetchRecord(userId);
		 return Result.OK(s);
	 }

	/**
	 *   添加
	 *
	 * @param pdChat
	 * @return
	 */
	@AutoLog(value = "聊天记录-添加")
	@ApiOperation(value="聊天记录-添加", notes="聊天记录-添加")
	@RequiresPermissions("wechat:pd_chat:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdChat pdChat) {
		pdChatService.save(pdChat);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdChat
	 * @return
	 */
	@AutoLog(value = "聊天记录-编辑")
	@ApiOperation(value="聊天记录-编辑", notes="聊天记录-编辑")
	@RequiresPermissions("wechat:pd_chat:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdChat pdChat) {
		pdChatService.updateById(pdChat);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "聊天记录-通过id删除")
	@ApiOperation(value="聊天记录-通过id删除", notes="聊天记录-通过id删除")
	@RequiresPermissions("wechat:pd_chat:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdChatService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "聊天记录-批量删除")
	@ApiOperation(value="聊天记录-批量删除", notes="聊天记录-批量删除")
	@RequiresPermissions("wechat:pd_chat:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdChatService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "聊天记录-通过id查询")
	@ApiOperation(value="聊天记录-通过id查询", notes="聊天记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdChat> queryById(@RequestParam(name="id",required=true) String id) {
		PdChat pdChat = pdChatService.getById(id);
		if(pdChat==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdChat);
	}

    /**
    * 导出excel - 优化版本，支持百万级数据导出
    *
    * @param request
    * @param response
    * @param pdChat
    */
    @RequiresPermissions("wechat:pd_chat:exportXls")
    @RequestMapping(value = "/exportXls")
    public void exportXls(HttpServletRequest request, HttpServletResponse response, PdChat pdChat) {
        try {
            // Step.1 组装查询条件
            QueryWrapper<PdChat> queryWrapper = buildQueryWrapper(request);

            // Step.2 统计总数据量
            long totalCount = pdChatService.count(queryWrapper);
            log.info("开始导出聊天记录，总数据量: {}", totalCount);

            if (totalCount == 0) {
                // 如果没有数据，导出空文件
                exportEmptyFile(response);
                return;
            }

            // Step.3 根据数据量选择导出策略
            if (totalCount <= excelExportConfig.getSingleSheetThreshold()) {
                // 配置阈值以下数据，使用单sheet导出
                exportSingleSheet(queryWrapper, response);
            } else {
                // 配置阈值以上数据，使用多sheet分页导出
                exportMultipleSheets(queryWrapper, response, totalCount);
            }

            // 内存使用监控
            if (excelExportConfig.shouldWarnMemoryUsage()) {
                log.warn("导出完成后内存使用较高: {}MB，建议关注内存情况",
                        excelExportConfig.getCurrentMemoryUsage());
            }

        } catch (Exception e) {
            log.error("导出聊天记录失败", e);
            handleExportError(response, e);
        }
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<PdChat> buildQueryWrapper(HttpServletRequest request) {
        QueryWrapper<PdChat> queryWrapper = new QueryWrapper<>();

        // 租户过滤
        String tenantId = request.getParameter("tenantId");
        if (StringUtils.isNotBlank(tenantId)) {
            queryWrapper.eq("c.tenant_id", tenantId);
        }

        // 时间范围过滤
        String dateRange = request.getParameter("dateRange");
        if (StringUtils.isNotBlank(dateRange)) {
            String[] dates = dateRange.split(",");
            if (dates.length == 2) {
                String startDate = dates[0].trim();
                String endDate = dates[1].trim();
                queryWrapper.ge("c.send_time", startDate + " 00:00:00");
                queryWrapper.le("c.send_time", endDate + " 23:59:59");
            }
        }

        // 选中数据过滤
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("c.id", selectionList);
        }

        return queryWrapper;
    }

    /**
     * 导出空文件
     */
    private void exportEmptyFile(HttpServletResponse response) {
        List<List<String>> emptyData = new ArrayList<>();
        List<String> header = Arrays.asList("租户名称", "用户名称", "消息内容", "发送方", "发送时间", "创建时间");
        emptyData.add(header);

        String fileName = "聊天记录" + ExcelUtils.getExcelTime();
        ExcelUtils.writeExcelSimple(response, fileName, "聊天记录", emptyData);
    }

    /**
     * 单sheet导出（适用于配置阈值以下数据）
     */
    private void exportSingleSheet(QueryWrapper<PdChat> queryWrapper, HttpServletResponse response) {
        log.info("使用单sheet导出模式，阈值: {}", excelExportConfig.getSingleSheetThreshold());

        // 分页查询，避免一次性加载过多数据到内存
        int pageSize = excelExportConfig.getPageSize();
        int pageNum = 1;
        List<List<String>> allExcelData = new ArrayList<>();

        // 添加表头
        List<String> header = Arrays.asList("租户名称", "用户名称", "消息内容", "发送方", "发送时间", "创建时间");
        allExcelData.add(header);

        Page<PdChat> page = new Page<>(pageNum, pageSize);
        IPage<PdChat> pageResult;
        long processedRows = 0;

        do {
            page.setCurrent(pageNum);
            pageResult = pdChatService.pageList(page, queryWrapper);

            if (pageResult.getRecords().isEmpty()) {
                break;
            }

            // 处理当前页数据
            List<List<String>> pageData = convertToExcelData(pageResult.getRecords());
            allExcelData.addAll(pageData);
            processedRows += pageData.size();

            // 内存监控
            if (excelExportConfig.isEnableProgressMonitor()) {
                long currentMemory = excelExportConfig.getCurrentMemoryUsage();
                log.info("已处理第{}页数据，当前页数据量: {}，累计处理: {}行，内存使用: {}MB",
                        pageNum, pageResult.getRecords().size(), processedRows, currentMemory);

                if (currentMemory > excelExportConfig.getMemoryWarningThreshold()) {
                    log.warn("内存使用超过警告阈值: {}MB", currentMemory);
                }
            }

            pageNum++;

        } while (pageResult.hasNext());

        // 导出Excel
        String fileName = "聊天记录" + ExcelUtils.getExcelTime();
        ExcelUtils.writeExcelSimple(response, fileName, "聊天记录", allExcelData);

        log.info("单sheet导出完成，总数据量: {}行", processedRows);
    }

    /**
     * 多sheet导出（适用于10万以上数据）
     */
    private void exportMultipleSheets(QueryWrapper<PdChat> queryWrapper, HttpServletResponse response, long totalCount) {
        log.info("使用多sheet导出模式，总数据量: {}", totalCount);

        try {
            // 设置响应头
            String fileName = "聊天记录" + ExcelUtils.getExcelTime();
            ExcelUtils.setResponse(response, fileName);

            // 每个sheet最大行数（Excel限制1048576行，我们设置为50万行，留有余量）
            int maxRowsPerSheet = 500000;
            // 每次查询的页大小
            int pageSize = 10000;

            // 计算需要的sheet数量
            int totalSheets = (int) Math.ceil((double) totalCount / maxRowsPerSheet);
            log.info("预计生成{}个sheet", totalSheets);

            // 使用EasyExcel的ExcelWriter进行多sheet写入
            try (com.alibaba.excel.ExcelWriter excelWriter = com.alibaba.excel.EasyExcel
                    .write(response.getOutputStream())
                    .excelType(com.alibaba.excel.support.ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE)
                    .build()) {

                for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                    // 创建sheet
                    com.alibaba.excel.write.metadata.WriteSheet writeSheet = com.alibaba.excel.EasyExcel
                            .writerSheet(sheetIndex, "聊天记录_" + (sheetIndex + 1))
                            .build();

                    // 写入表头
                    List<List<String>> sheetData = new ArrayList<>();
                    List<String> header = Arrays.asList("租户名称", "用户名称", "消息内容", "发送方", "发送时间", "创建时间");
                    sheetData.add(header);

                    // 计算当前sheet的数据范围
                    long startRow = (long) sheetIndex * maxRowsPerSheet;
                    long endRow = Math.min(startRow + maxRowsPerSheet, totalCount);

                    log.info("开始处理第{}个sheet，数据范围: {} - {}", sheetIndex + 1, startRow, endRow);

                    // 分页查询当前sheet的数据
                    long currentSheetRows = 0;
                    int pageNum = (int) (startRow / pageSize) + 1;

                    while (currentSheetRows < maxRowsPerSheet && (startRow + currentSheetRows) < totalCount) {
                        Page<PdChat> page = new Page<>(pageNum, pageSize);
                        IPage<PdChat> pageResult = pdChatService.pageList(page, queryWrapper);

                        if (pageResult.getRecords().isEmpty()) {
                            break;
                        }

                        // 转换数据并添加到当前sheet
                        List<List<String>> pageData = convertToExcelData(pageResult.getRecords());

                        // 检查是否超出当前sheet的容量
                        long remainingRows = maxRowsPerSheet - currentSheetRows;
                        if (pageData.size() > remainingRows) {
                            // 只取需要的行数
                            pageData = pageData.subList(0, (int) remainingRows);
                        }

                        sheetData.addAll(pageData);
                        currentSheetRows += pageData.size();

                        log.info("第{}个sheet已处理{}行数据", sheetIndex + 1, currentSheetRows);
                        pageNum++;
                    }

                    // 写入当前sheet的数据
                    excelWriter.write(sheetData, writeSheet);
                    log.info("第{}个sheet写入完成，实际数据行数: {}", sheetIndex + 1, currentSheetRows);
                }

                log.info("多sheet导出完成，共生成{}个sheet", totalSheets);
            }

        } catch (Exception e) {
            log.error("多sheet导出失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将PdChat数据转换为Excel行数据
     */
    private List<List<String>> convertToExcelData(List<PdChat> chatList) {
        if (chatList == null || chatList.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有需要查询的租户ID和用户ID
        Set<Integer> tenantIds = chatList.stream()
                .map(PdChat::getTenantId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<String> userIds = chatList.stream()
                .map(PdChat::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询租户和用户信息，减少数据库查询次数
        Map<Integer, SysTenant> tenantMap = new HashMap<>();
        if (!tenantIds.isEmpty()) {
            tenantMap = sysTenantService.listByIds(tenantIds)
                    .stream()
                    .collect(Collectors.toMap(SysTenant::getId, Function.identity()));
        }

        Map<String, PdGuestUsers> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            userMap = pdGuestUsersService.listByIds(userIds)
                    .stream()
                    .collect(Collectors.toMap(PdGuestUsers::getId, Function.identity()));
        }

        // 转换数据
        List<List<String>> excelData = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (PdChat item : chatList) {
            List<String> row = new ArrayList<>();

            // 租户名称
            SysTenant tenant = tenantMap.get(item.getTenantId());
            row.add(tenant != null ? tenant.getName() : "");

            // 用户名称
            PdGuestUsers guestUser = userMap.get(item.getUserId());
            row.add(guestUser != null ? guestUser.getName() : "");

            // 消息内容
            row.add(item.getMessage() != null ? item.getMessage() : "");

            // 发送方
            String sendTypeStr = "";
            if (item.getSendType() != null) {
                sendTypeStr = item.getSendType() == 0 ? "用户" : "客服";
            }
            row.add(sendTypeStr);

            // 发送时间
            row.add(item.getSendTime() != null ? dateFormat.format(item.getSendTime()) : "");

            // 创建时间
            row.add(item.getCreateTime() != null ? dateFormat.format(item.getCreateTime()) : "");

            excelData.add(row);
        }

        return excelData;
    }

    /**
     * 处理导出错误
     */
    private void handleExportError(HttpServletResponse response, Exception e) {
        try {
            response.reset();
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"success\":false,\"message\":\"导出失败: " + e.getMessage() + "\"}");
        } catch (Exception ex) {
            log.error("处理导出错误时发生异常", ex);
        }
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wechat:pd_chat:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdChat.class);
    }

}
