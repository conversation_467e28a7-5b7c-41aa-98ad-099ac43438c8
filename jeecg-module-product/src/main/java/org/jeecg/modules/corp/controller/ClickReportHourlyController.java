package org.jeecg.modules.corp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
* <p>
 * 按小时统计点击报表前端控制器
 * </p>
*
* <AUTHOR>
* @since 2025-05-12
*/
@Api(tags = "按小时统计点击报表")
@RestController
@Slf4j
@RequestMapping("click-report-hourly")
public class ClickReportHourlyController {
    @Autowired
    private IClickReportHourlyService iClickReportHourlyService;

//    @ApiOperation("新增按小时统计点击报表")
//    @PostMapping("/add")
//    public DataResult<ClickReportHourly> addClickReportHourly(@Valid @RequestBody ClickReportHourly dto) {
//        return SysHttpResult.SUCCESS.getDataResult(iClickReportHourlyService.add(dto));
//    }
//
//    @ApiOperation("修改按小时统计点击报表")
//    @PutMapping("/edit/{id}")
//    @ApiImplicitParam(value = "按小时统计点击报表id",name = "id",dataTypeClass = String.class)
//    public DataResult<ClickReportHourly> editClickReportHourly(@PathVariable String id, @Valid @RequestBody ClickReportHourly dto) {
//        dto.setId(id);
//        return SysHttpResult.SUCCESS.getDataResult(iClickReportHourlyService.edit(dto));
//    }
//
//
//    @ApiOperation("删除按小时统计点击报表")
//    @DeleteMapping("/delete/{id}")
//    @ApiImplicitParam(value = "按小时统计点击报表id",name = "id",dataTypeClass = String.class)
//    public DataResult deleteClickReportHourly(@PathVariable String id) {
//        iClickReportHourlyService.deleteById(id);
//        return SysHttpResult.SUCCESS.getDataResult();
//    }





}