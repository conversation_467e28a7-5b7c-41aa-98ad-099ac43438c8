package org.jeecg.modules.corp.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 增值服务预约记录
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Data
@TableName("pd_added")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_added对象", description="增值服务预约记录")
public class PdAdded implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**ip地址*/
	@Excel(name = "ip地址", width = 15)
    @ApiModelProperty(value = "ip地址")
    private java.lang.String ipAddress;
	/**链接类型*/
	@Excel(name = "链接类型", width = 15)
    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)")
    private java.lang.Integer linkType;
	/**游客id*/
	@Excel(name = "游客id", width = 15)
    @ApiModelProperty(value = "游客id")
    private java.lang.String guestId;
	/**用户来源*/
	@Excel(name = "用户来源", width = 15)
    @ApiModelProperty(value = "用户来源")
    private java.lang.Integer source;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
    @ApiModelProperty(value = "游客名称")
    private  java.lang.String guestName;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private java.lang.String phone;
	/**链接 id*/
	@Excel(name = "链接 id", width = 15)
    @ApiModelProperty(value = "链接 id")
    private java.lang.String linkId;
    @ApiModelProperty(value = "台账 id")
    private java.lang.String ledgerId;
	/**选择服务*/
	@Excel(name = "选择服务", width = 15)
    @ApiModelProperty(value = "选择服务")
    private java.lang.String serve;
    @ApiModelProperty(value = "是否已生成")
    private  Integer isVied;
	/**多租户*/
	@Excel(name = "多租户", width = 15)
    @ApiModelProperty(value = "多租户")
    private java.lang.Integer tenantId;
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;
    /**是否存在聊天用户（0=否，1=是）*/
    private transient Integer hasChatUser;
}
