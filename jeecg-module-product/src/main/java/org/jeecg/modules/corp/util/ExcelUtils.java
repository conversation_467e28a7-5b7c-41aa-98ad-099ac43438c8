package org.jeecg.modules.corp.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class ExcelUtils {

    /**
     * 读取Excel文件
     */
    public static <T> List<T> read(MultipartFile file, Class<T> clazz) {
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReaderBuilder builder = EasyExcel.read(inputStream, clazz, null).headRowNumber(3);
            return builder.doReadAllSync();
        } catch (Exception e) {
            throw new RuntimeException("读取Excel失败", e);
        }
    }

    /**
     * 写入excel
     *
     * @param response
     * @param fileName
     * @param sheetName
     * @param T
     * @param list
     */
    public static void writeExcel(HttpServletResponse response, String fileName, String sheetName, Class T, List list) {
        try {
            setResponse(response, fileName);

            EasyExcel.write(response.getOutputStream(), T)
                    // excel版本
                    .excelType(ExcelTypeEnum.XLSX)
                    //是否自动关流
                    .autoCloseStream(Boolean.TRUE)
                    .sheet(sheetName).doWrite(list);
        } catch (Exception e) {
            ErrorResult(response, e);
        }
    }

    /**
     * 写入excel - 简化版本，避免CGLib问题
     *
     * @param response
     * @param fileName
     * @param sheetName
     * @param list
     */
    public static void writeExcelSimple(HttpServletResponse response, String fileName, String sheetName, List<List<String>> list) {
        try {
            setResponse(response, fileName);

            EasyExcel.write(response.getOutputStream())
                    // excel版本
                    .excelType(ExcelTypeEnum.XLSX)
                    //是否自动关流
                    .autoCloseStream(Boolean.TRUE)
                    .sheet(sheetName).doWrite(list);
        } catch (Exception e) {
            ErrorResult(response, e);
        }
    }

    /**
     * 流式写入Excel - 适用于大数据量导出
     * 使用回调函数分批获取数据，避免内存溢出
     *
     * @param response HTTP响应
     * @param fileName 文件名
     * @param sheetName sheet名称
     * @param totalCount 总数据量
     * @param pageSize 每页大小
     * @param dataProvider 数据提供者回调函数，参数为页码，返回该页的数据
     */
    public static void writeExcelStream(HttpServletResponse response, String fileName, String sheetName,
                                       long totalCount, int pageSize,
                                       java.util.function.Function<Integer, List<List<String>>> dataProvider) {
        try {
            setResponse(response, fileName);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);

            try (com.alibaba.excel.ExcelWriter excelWriter = EasyExcel
                    .write(response.getOutputStream())
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE)
                    .build()) {

                com.alibaba.excel.write.metadata.WriteSheet writeSheet = EasyExcel
                        .writerSheet(0, sheetName)
                        .build();

                // 分页写入数据
                for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                    List<List<String>> pageData = dataProvider.apply(pageNum);
                    if (pageData != null && !pageData.isEmpty()) {
                        excelWriter.write(pageData, writeSheet);
                    }
                }
            }
        } catch (Exception e) {
            ErrorResult(response, e);
        }
    }

    /**
     * 多sheet流式写入Excel - 适用于百万级数据导出
     *
     * @param response HTTP响应
     * @param fileName 文件名
     * @param totalCount 总数据量
     * @param maxRowsPerSheet 每个sheet最大行数
     * @param pageSize 每页大小
     * @param dataProvider 数据提供者回调函数
     */
    public static void writeExcelMultiSheet(HttpServletResponse response, String fileName,
                                           long totalCount, int maxRowsPerSheet, int pageSize,
                                           java.util.function.BiFunction<Integer, Integer, List<List<String>>> dataProvider) {
        try {
            setResponse(response, fileName);

            // 计算需要的sheet数量
            int totalSheets = (int) Math.ceil((double) totalCount / maxRowsPerSheet);

            try (com.alibaba.excel.ExcelWriter excelWriter = EasyExcel
                    .write(response.getOutputStream())
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE)
                    .build()) {

                for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                    com.alibaba.excel.write.metadata.WriteSheet writeSheet = EasyExcel
                            .writerSheet(sheetIndex, "数据_" + (sheetIndex + 1))
                            .build();

                    // 计算当前sheet的数据范围
                    long startRow = (long) sheetIndex * maxRowsPerSheet;
                    long endRow = Math.min(startRow + maxRowsPerSheet, totalCount);

                    // 分页获取当前sheet的数据
                    long currentSheetRows = 0;
                    int startPage = (int) (startRow / pageSize) + 1;

                    for (int pageNum = startPage; currentSheetRows < maxRowsPerSheet && (startRow + currentSheetRows) < totalCount; pageNum++) {
                        List<List<String>> pageData = dataProvider.apply(sheetIndex, pageNum);

                        if (pageData == null || pageData.isEmpty()) {
                            break;
                        }

                        // 检查是否超出当前sheet的容量
                        long remainingRows = maxRowsPerSheet - currentSheetRows;
                        if (pageData.size() > remainingRows) {
                            pageData = pageData.subList(0, (int) remainingRows);
                        }

                        excelWriter.write(pageData, writeSheet);
                        currentSheetRows += pageData.size();
                    }
                }
            }
        } catch (Exception e) {
            ErrorResult(response, e);
        }
    }

    /**
     * 获取Excel导出时间
     *
     * @return
     */
    public static String getExcelTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return sdf.format(new Date());
    }

    /**
     * 设置Response
     *
     * @param response
     * @param name
     * @throws UnsupportedEncodingException
     */
    public static void setResponse(HttpServletResponse response, String name) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(name, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
    }

    /**
     * 错误返回
     *
     * @param response
     * @param e
     */
    private static void ErrorResult(HttpServletResponse response, Exception e) {
        e.printStackTrace();
        response.reset();
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
    }
}
