package org.jeecg.modules.corp.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.corp.dto.DailyConfigDto;
import org.jeecg.modules.corp.entity.ClickAutoPre;
import org.jeecg.modules.corp.entity.DailyConfig;
import org.jeecg.modules.corp.service.IClickAutoPreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.util.List;

/**
* @Description: 点击数预生成
* @Author: jeecg-boot
* @Date:   2024-11-12
* @Version: V1.0
*/
@Api(tags="点击数预生成")
@RestController
@RequestMapping("/clickAuto/pre")
@Slf4j
public class ClickPreController extends JeecgController<ClickAutoPre, IClickAutoPreService> {
   @Autowired
   private IClickAutoPreService preService;


   /**
    *   定时添加配置
    * @return
    */
   @PostMapping(value = "/add")
   public Result<String> add(@RequestBody DailyConfig dailyConfig) throws JsonProcessingException {
       preService.autoCreateClickPre(dailyConfig);
       return Result.OK("添加成功！");
   }

    /**
     *   定时新增
     * @return
     */
    @PostMapping(value = "/autoCreateClick")
    public Result<String> autoCreateClick(@RequestBody DailyConfig dailyConfig) throws ParseException {
        preService.autoCreateClick(dailyConfig);
        return Result.OK("添加成功！");
    }
    @ApiOperation(value="批量系统补全-根据表格文件")
    @PostMapping(value = {"/uploadFile/multiBatch"})
    public Result uploadMultipleFiles(@RequestPart(value = "files", required = true) MultipartFile files) throws JsonProcessingException {
        preService.uploadMultipleFiles(files);
        return Result.OK("添加成功！");
    }


    @ApiOperation(value="系统补全")
    @PostMapping(value = {"/uploadFile/multi"})
    public Result uploadMultipleFiles(@RequestPart(value = "files", required = false) List<MultipartFile> files, DailyConfigDto dto) throws JsonProcessingException {
        preService.importLedgers(files,dto);
        return Result.OK("添加成功！");
    }

    @ApiOperation(value="查询批量任务处理进度")
    @GetMapping(value = "/batchProgress")
    public Result<java.util.Map<String, Object>> getBatchProgress() {
        // 调用服务层方法获取进度信息
        java.util.Map<String, Object> progressInfo = preService.getBatchProcessProgress();
        return Result.OK(progressInfo);
    }
}
